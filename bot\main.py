import asyncio
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage
from loguru import logger

from config.settings import settings
from database.models import db
from bot.handlers import router
from bot.moderation import create_moderation_service

class TelegramBot:
    def __init__(self):
        self.bot = Bot(token=settings.BOT_TOKEN)
        self.dp = Dispatcher(storage=MemoryStorage())
        self.moderation_service = create_moderation_service(self.bot)
        
        # Регистрируем роутеры
        self.dp.include_router(router)
    
    async def start(self):
        """Запуск бота"""
        logger.info("Starting Telegram bot...")
        
        # Инициализируем базу данных
        await db.init_db()
        
        # Запускаем сервис модерации
        asyncio.create_task(self.moderation_service.start_monitoring())
        
        # Запускаем бота
        await self.dp.start_polling(self.bot)
    
    async def stop(self):
        """Остановка бота"""
        logger.info("Stopping Telegram bot...")
        await self.bot.session.close()

# Глобальный экземпляр бота
telegram_bot = TelegramBot()
