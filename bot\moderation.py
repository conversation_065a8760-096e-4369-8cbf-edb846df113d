import json
import asyncio
import os
from aiogram import Bo<PERSON>
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from loguru import logger
from config.settings import settings

class ModerationService:
    def __init__(self, bot: Bot):
        self.bot = bot
        self.queue_file = './data/moderation_queue.jsonl'
        self.processed_file = './data/processed_moderation.jsonl'
    
    async def start_monitoring(self):
        """Запуск мониторинга очереди модерации"""
        logger.info("Starting moderation monitoring...")
        
        while True:
            try:
                await self._process_moderation_queue()
                await asyncio.sleep(5)  # Проверяем каждые 5 секунд
            except Exception as e:
                logger.error(f"Error in moderation monitoring: {e}")
                await asyncio.sleep(10)
    
    async def _process_moderation_queue(self):
        """Обработка очереди модерации"""
        if not os.path.exists(self.queue_file):
            return
        
        # Читаем все строки из файла
        with open(self.queue_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if not lines:
            return
        
        # Обрабатываем каждую строку
        processed_lines = []
        
        for line in lines:
            try:
                data = json.loads(line.strip())
                
                # Отправляем на модерацию
                await self._send_moderation_message(data)
                
                # Помечаем как обработанное
                processed_lines.append(line)
                
            except Exception as e:
                logger.error(f"Error processing moderation line: {e}")
        
        # Очищаем файл очереди
        if processed_lines:
            open(self.queue_file, 'w').close()
            
            # Сохраняем обработанные в отдельный файл
            with open(self.processed_file, 'a', encoding='utf-8') as f:
                f.writelines(processed_lines)
    
    async def _send_moderation_message(self, data):
        """Отправка сообщения на модерацию"""
        post_id = data['post_id']
        original_text = data['original_text']
        processed_text = data['processed_text']
        
        # Создаем клавиатуру для модерации
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Одобрить",
                    callback_data=f"approve_{post_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Отклонить", 
                    callback_data=f"reject_{post_id}"
                )
            ]
        ])
        
        # Формируем сообщение
        message_text = f"""
🔍 <b>Новый пост на модерацию</b>
📝 <b>ID:</b> {post_id}

📄 <b>Оригинальный текст:</b>
<code>{self._truncate_text(original_text, 300)}</code>

✨ <b>Обработанный текст:</b>
<code>{self._truncate_text(processed_text, 300)}</code>

❓ <b>Опубликовать этот пост?</b>
"""
        
        try:
            await self.bot.send_message(
                chat_id=settings.ADMIN_ID,
                text=message_text,
                parse_mode="HTML",
                reply_markup=keyboard
            )
            
            logger.info(f"Moderation message sent for post {post_id}")
            
        except Exception as e:
            logger.error(f"Error sending moderation message: {e}")
    
    def _truncate_text(self, text: str, max_length: int) -> str:
        """Обрезка текста до максимальной длины"""
        if len(text) <= max_length:
            return text
        return text[:max_length] + "..."

# Функция для создания сервиса модерации
def create_moderation_service(bot: Bot) -> ModerationService:
    return ModerationService(bot)
