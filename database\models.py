import aiosqlite
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from config.settings import settings

@dataclass
class Channel:
    id: int
    username: str
    title: str
    is_active: bool = True
    added_date: datetime = None

@dataclass
class Post:
    id: int
    channel_id: int
    original_message_id: int
    original_text: str
    processed_text: str = None
    status: str = "pending"  # pending, approved, rejected, published
    created_date: datetime = None
    processed_date: datetime = None
    media_paths: str = None  # JSON string with media file paths

@dataclass
class AdTemplate:
    id: int
    name: str
    template: str
    is_active: bool = True
    created_date: datetime = None

class Database:
    def __init__(self, db_path: str = None):
        self.db_path = db_path or settings.DATABASE_PATH
    
    async def init_db(self):
        """Инициализация базы данных"""
        async with aiosqlite.connect(self.db_path) as db:
            # Таблица каналов для мониторинга
            await db.execute("""
                CREATE TABLE IF NOT EXISTS channels (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Таблица постов
            await db.execute("""
                CREATE TABLE IF NOT EXISTS posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id INTEGER NOT NULL,
                    original_message_id INTEGER NOT NULL,
                    original_text TEXT NOT NULL,
                    processed_text TEXT,
                    status TEXT DEFAULT 'pending',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_date TIMESTAMP,
                    media_paths TEXT,
                    FOREIGN KEY (channel_id) REFERENCES channels (id)
                )
            """)
            
            # Таблица шаблонов рекламы
            await db.execute("""
                CREATE TABLE IF NOT EXISTS ad_templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    template TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            await db.commit()
    
    async def add_channel(self, channel_id: int, username: str, title: str) -> bool:
        """Добавить канал для мониторинга"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "INSERT OR REPLACE INTO channels (id, username, title) VALUES (?, ?, ?)",
                    (channel_id, username, title)
                )
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding channel: {e}")
            return False
    
    async def get_active_channels(self) -> List[Channel]:
        """Получить активные каналы для мониторинга"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT id, username, title, is_active, added_date FROM channels WHERE is_active = 1"
            ) as cursor:
                rows = await cursor.fetchall()
                return [Channel(*row) for row in rows]
    
    async def add_post(self, channel_id: int, message_id: int, text: str, media_paths: str = None) -> int:
        """Добавить новый пост"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "INSERT INTO posts (channel_id, original_message_id, original_text, media_paths) VALUES (?, ?, ?, ?)",
                (channel_id, message_id, text, media_paths)
            )
            await db.commit()
            return cursor.lastrowid
    
    async def update_post_processed(self, post_id: int, processed_text: str):
        """Обновить обработанный текст поста"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                "UPDATE posts SET processed_text = ?, processed_date = CURRENT_TIMESTAMP WHERE id = ?",
                (processed_text, post_id)
            )
            await db.commit()
    
    async def update_post_status(self, post_id: int, status: str):
        """Обновить статус поста"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                "UPDATE posts SET status = ? WHERE id = ?",
                (status, post_id)
            )
            await db.commit()
    
    async def get_post(self, post_id: int) -> Optional[Post]:
        """Получить пост по ID"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT * FROM posts WHERE id = ?", (post_id,)
            ) as cursor:
                row = await cursor.fetchone()
                if row:
                    return Post(*row)
                return None
    
    async def get_pending_posts(self) -> List[Post]:
        """Получить посты ожидающие модерации"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT * FROM posts WHERE status = 'pending' ORDER BY created_date"
            ) as cursor:
                rows = await cursor.fetchall()
                return [Post(*row) for row in rows]

# Глобальный экземпляр базы данных
db = Database()
