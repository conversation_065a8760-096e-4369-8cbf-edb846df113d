import aiosqlite
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from config.settings import settings

@dataclass
class Channel:
    id: int
    username: str
    title: str
    is_active: bool = True
    added_date: datetime = None

@dataclass
class TargetChannel:
    id: int
    username: str
    title: str
    ad_template: str = None
    is_active: bool = True
    added_date: datetime = None

@dataclass
class ChannelMapping:
    id: int
    donor_channel_id: int
    target_channel_id: int
    is_active: bool = True
    created_date: datetime = None

@dataclass
class Post:
    id: int
    channel_id: int
    original_message_id: int
    original_text: str
    processed_text: str = None
    status: str = "pending"  # pending, approved, rejected, published
    created_date: datetime = None
    processed_date: datetime = None
    media_paths: str = None  # JSON string with media file paths

@dataclass
class AdTemplate:
    id: int
    name: str
    template: str
    is_active: bool = True
    created_date: datetime = None

class Database:
    def __init__(self, db_path: str = None):
        self.db_path = db_path or settings.DATABASE_PATH

    async def init_db(self):
        """Инициализация базы данных"""
        async with aiosqlite.connect(self.db_path) as db:
            # Таблица каналов-доноров для мониторинга
            await db.execute("""
                CREATE TABLE IF NOT EXISTS channels (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Таблица целевых каналов для публикации
            await db.execute("""
                CREATE TABLE IF NOT EXISTS target_channels (
                    id INTEGER PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    title TEXT NOT NULL,
                    ad_template TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Таблица связей донор -> целевой канал
            await db.execute("""
                CREATE TABLE IF NOT EXISTS channel_mappings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    donor_channel_id INTEGER NOT NULL,
                    target_channel_id INTEGER NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (donor_channel_id) REFERENCES channels (id),
                    FOREIGN KEY (target_channel_id) REFERENCES target_channels (id),
                    UNIQUE(donor_channel_id, target_channel_id)
                )
            """)

            # Таблица постов
            await db.execute("""
                CREATE TABLE IF NOT EXISTS posts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id INTEGER NOT NULL,
                    target_channel_id INTEGER,
                    original_message_id INTEGER NOT NULL,
                    original_text TEXT NOT NULL,
                    processed_text TEXT,
                    status TEXT DEFAULT 'pending',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_date TIMESTAMP,
                    published_date TIMESTAMP,
                    media_paths TEXT,
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    FOREIGN KEY (target_channel_id) REFERENCES target_channels (id)
                )
            """)

            # Таблица шаблонов рекламы
            await db.execute("""
                CREATE TABLE IF NOT EXISTS ad_templates (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    template TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            await db.commit()

    async def add_channel(self, channel_id: int, username: str, title: str) -> bool:
        """Добавить канал для мониторинга"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "INSERT OR REPLACE INTO channels (id, username, title) VALUES (?, ?, ?)",
                    (channel_id, username, title)
                )
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding channel: {e}")
            return False

    async def get_active_channels(self) -> List[Channel]:
        """Получить активные каналы для мониторинга"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT id, username, title, is_active, added_date FROM channels WHERE is_active = 1"
            ) as cursor:
                rows = await cursor.fetchall()
                return [Channel(*row) for row in rows]

    async def add_post(self, channel_id: int, message_id: int, text: str, media_paths: str = None) -> int:
        """Добавить новый пост"""
        async with aiosqlite.connect(self.db_path) as db:
            cursor = await db.execute(
                "INSERT INTO posts (channel_id, original_message_id, original_text, media_paths) VALUES (?, ?, ?, ?)",
                (channel_id, message_id, text, media_paths)
            )
            await db.commit()
            return cursor.lastrowid

    async def update_post_processed(self, post_id: int, processed_text: str):
        """Обновить обработанный текст поста"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                "UPDATE posts SET processed_text = ?, processed_date = CURRENT_TIMESTAMP WHERE id = ?",
                (processed_text, post_id)
            )
            await db.commit()

    async def update_post_status(self, post_id: int, status: str):
        """Обновить статус поста"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(
                "UPDATE posts SET status = ? WHERE id = ?",
                (status, post_id)
            )
            await db.commit()

    async def get_post(self, post_id: int) -> Optional[Post]:
        """Получить пост по ID"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT * FROM posts WHERE id = ?", (post_id,)
            ) as cursor:
                row = await cursor.fetchone()
                if row:
                    return Post(*row)
                return None

    async def get_pending_posts(self) -> List[Post]:
        """Получить посты ожидающие модерации"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT * FROM posts WHERE status = 'pending' ORDER BY created_date"
            ) as cursor:
                rows = await cursor.fetchall()
                return [Post(*row) for row in rows]

    # Методы для работы с целевыми каналами
    async def add_target_channel(self, channel_id: int, username: str, title: str, ad_template: str = None) -> bool:
        """Добавить целевой канал для публикации"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "INSERT OR REPLACE INTO target_channels (id, username, title, ad_template) VALUES (?, ?, ?, ?)",
                    (channel_id, username, title, ad_template)
                )
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding target channel: {e}")
            return False

    async def get_target_channels(self) -> List[TargetChannel]:
        """Получить все целевые каналы"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT id, username, title, ad_template, is_active, added_date FROM target_channels WHERE is_active = 1"
            ) as cursor:
                rows = await cursor.fetchall()
                return [TargetChannel(*row) for row in rows]

    async def get_target_channel(self, channel_id: int) -> Optional[TargetChannel]:
        """Получить целевой канал по ID"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute(
                "SELECT id, username, title, ad_template, is_active, added_date FROM target_channels WHERE id = ?",
                (channel_id,)
            ) as cursor:
                row = await cursor.fetchone()
                if row:
                    return TargetChannel(*row)
                return None

    # Методы для работы с маппингами каналов
    async def add_channel_mapping(self, donor_id: int, target_id: int) -> bool:
        """Связать канал-донор с целевым каналом"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "INSERT OR REPLACE INTO channel_mappings (donor_channel_id, target_channel_id) VALUES (?, ?)",
                    (donor_id, target_id)
                )
                await db.commit()
                return True
        except Exception as e:
            print(f"Error adding channel mapping: {e}")
            return False

    async def get_target_channels_for_donor(self, donor_id: int) -> List[TargetChannel]:
        """Получить целевые каналы для конкретного донора"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("""
                SELECT tc.id, tc.username, tc.title, tc.ad_template, tc.is_active, tc.added_date
                FROM target_channels tc
                JOIN channel_mappings cm ON tc.id = cm.target_channel_id
                WHERE cm.donor_channel_id = ? AND cm.is_active = 1 AND tc.is_active = 1
            """, (donor_id,)) as cursor:
                rows = await cursor.fetchall()
                return [TargetChannel(*row) for row in rows]

    async def remove_channel_mapping(self, donor_id: int, target_id: int) -> bool:
        """Удалить связь между каналами"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute(
                    "UPDATE channel_mappings SET is_active = 0 WHERE donor_channel_id = ? AND target_channel_id = ?",
                    (donor_id, target_id)
                )
                await db.commit()
                return True
        except Exception as e:
            print(f"Error removing channel mapping: {e}")
            return False

    async def get_all_mappings(self) -> List[dict]:
        """Получить все активные маппинги"""
        async with aiosqlite.connect(self.db_path) as db:
            async with db.execute("""
                SELECT
                    c.username as donor_username,
                    c.title as donor_title,
                    tc.username as target_username,
                    tc.title as target_title,
                    cm.created_date
                FROM channel_mappings cm
                JOIN channels c ON cm.donor_channel_id = c.id
                JOIN target_channels tc ON cm.target_channel_id = tc.id
                WHERE cm.is_active = 1
                ORDER BY cm.created_date DESC
            """) as cursor:
                rows = await cursor.fetchall()
                return [
                    {
                        'donor_username': row[0],
                        'donor_title': row[1],
                        'target_username': row[2],
                        'target_title': row[3],
                        'created_date': row[4]
                    }
                    for row in rows
                ]

# Глобальный экземпляр базы данных
db = Database()
