#!/usr/bin/env python3
"""
Простой скрипт для получения вашего Telegram ID
"""

import asyncio
from telethon import TelegramClient
from config.settings import settings

async def get_my_id():
    """Получить свой Telegram ID"""
    print("🔍 Получение вашего Telegram ID...")
    
    client = TelegramClient(
        './sessions/temp_session',
        settings.API_ID,
        settings.API_HASH
    )
    
    try:
        await client.start()
        
        # Получаем информацию о себе
        me = await client.get_me()
        
        print(f"\n✅ Ваш Telegram ID: {me.id}")
        print(f"📝 Имя: {me.first_name} {me.last_name or ''}")
        print(f"👤 Username: @{me.username or 'не установлен'}")
        print(f"\n💡 Добавьте эту строку в ваш .env файл:")
        print(f"ADMIN_ID={me.id}")
        
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        print("Убедитесь, что API_ID и API_HASH правильно указаны в .env файле")
    
    finally:
        await client.disconnect()

if __name__ == "__main__":
    try:
        asyncio.run(get_my_id())
    except KeyboardInterrupt:
        print("\n👋 Отменено пользователем")
    except Exception as e:
        print(f"💥 Ошибка: {e}")
    
    input("\nНажмите Enter для выхода...")
