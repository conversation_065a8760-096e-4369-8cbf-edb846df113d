#!/usr/bin/env python3
"""
Утилиты для работы с медиа файлами и премиум эмодзи
"""

import os
import json
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from telethon.tl.types import (
    MessageMediaPhoto, MessageMediaDocument, MessageMediaWebPage,
    DocumentAttributeVideo, DocumentAttributeAnimated, DocumentAttributeSticker
)

class MediaHandler:
    def __init__(self, media_dir: str = "./data/media"):
        self.media_dir = Path(media_dir)
        self.media_dir.mkdir(parents=True, exist_ok=True)
    
    def detect_premium_emoji(self, text: str) -> bool:
        """Определить наличие премиум эмодзи в тексте"""
        if not text:
            return False
        
        # Премиум эмодзи имеют специальные Unicode диапазоны
        premium_emoji_patterns = [
            r'[\U0001F600-\U0001F64F]',  # Эмоции
            r'[\U0001F300-\U0001F5FF]',  # Символы и пиктограммы
            r'[\U0001F680-\U0001F6FF]',  # Транспорт и карты
            r'[\U0001F1E0-\U0001F1FF]',  # Флаги
            r'[\*********-\U000026FF]',  # Разные символы
            r'[\*********-\U000027BF]',  # Дингбаты
        ]
        
        # Проверяем на наличие сложных эмодзи (обычно премиум)
        complex_emoji_pattern = r'[\U0001F1E6-\U0001F1FF]{2}|[\U0001F3FB-\U0001F3FF]|[\U0000200D]'
        
        return bool(re.search(complex_emoji_pattern, text))
    
    def get_media_type(self, media) -> Optional[str]:
        """Определить тип медиа"""
        if not media:
            return None
        
        if isinstance(media, MessageMediaPhoto):
            return "photo"
        elif isinstance(media, MessageMediaDocument):
            if media.document:
                for attr in media.document.attributes:
                    if isinstance(attr, DocumentAttributeVideo):
                        return "video"
                    elif isinstance(attr, DocumentAttributeAnimated):
                        return "animation"
                    elif isinstance(attr, DocumentAttributeSticker):
                        return "sticker"
                return "document"
        elif isinstance(media, MessageMediaWebPage):
            return "webpage"
        
        return "unknown"
    
    async def download_media(self, client, message, post_id: int) -> Optional[Dict]:
        """Скачать медиа файлы"""
        if not message.media:
            return None
        
        try:
            media_info = {
                "type": self.get_media_type(message.media),
                "files": [],
                "post_id": post_id
            }
            
            # Создаем папку для поста
            post_dir = self.media_dir / f"post_{post_id}"
            post_dir.mkdir(exist_ok=True)
            
            # Скачиваем основной файл
            if media_info["type"] in ["photo", "video", "document", "animation"]:
                file_path = await client.download_media(
                    message.media,
                    file=str(post_dir)
                )
                
                if file_path:
                    media_info["files"].append({
                        "path": str(file_path),
                        "type": media_info["type"],
                        "size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    })
            
            # Сохраняем информацию о медиа
            info_file = post_dir / "media_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(media_info, f, ensure_ascii=False, indent=2)
            
            return media_info
            
        except Exception as e:
            print(f"Error downloading media: {e}")
            return None
    
    def get_media_info(self, post_id: int) -> Optional[Dict]:
        """Получить информацию о медиа поста"""
        info_file = self.media_dir / f"post_{post_id}" / "media_info.json"
        
        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error reading media info: {e}")
        
        return None
    
    def preserve_emoji_formatting(self, text: str) -> str:
        """Сохранить форматирование эмодзи при обработке"""
        if not text:
            return text
        
        # Сохраняем позиции всех эмодзи
        emoji_pattern = r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF\*********-\U000026FF\*********-\U000027BF]+'
        
        emojis = []
        for match in re.finditer(emoji_pattern, text):
            emojis.append({
                'emoji': match.group(),
                'start': match.start(),
                'end': match.end()
            })
        
        return text  # Возвращаем оригинальный текст с эмодзи
    
    async def prepare_media_for_publishing(self, post_id: int, target_channel_id: int) -> Optional[List[str]]:
        """Подготовить медиа для публикации"""
        media_info = self.get_media_info(post_id)
        
        if not media_info or not media_info.get("files"):
            return None
        
        # Возвращаем пути к файлам для публикации
        return [file_info["path"] for file_info in media_info["files"]]
    
    def cleanup_old_media(self, days_old: int = 7):
        """Очистить старые медиа файлы"""
        import time
        current_time = time.time()
        
        for post_dir in self.media_dir.iterdir():
            if post_dir.is_dir() and post_dir.name.startswith("post_"):
                # Проверяем возраст папки
                dir_age = current_time - post_dir.stat().st_mtime
                if dir_age > (days_old * 24 * 60 * 60):  # Конвертируем дни в секунды
                    try:
                        import shutil
                        shutil.rmtree(post_dir)
                        print(f"Cleaned up old media: {post_dir}")
                    except Exception as e:
                        print(f"Error cleaning up {post_dir}: {e}")

# Глобальный экземпляр обработчика медиа
media_handler = MediaHandler()
