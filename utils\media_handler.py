#!/usr/bin/env python3
"""
Утилиты для работы с медиа файлами и премиум эмодзи
"""

import os
import json
import re
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from telethon.tl.types import (
    MessageMediaPhoto, MessageMediaDocument, MessageMediaWebPage,
    DocumentAttributeVideo, DocumentAttributeAnimated, DocumentAttributeSticker
)

class MediaHandler:
    def __init__(self, media_dir: str = "./data/media"):
        self.media_dir = Path(media_dir)
        self.media_dir.mkdir(parents=True, exist_ok=True)

    def detect_premium_emoji(self, message) -> Tuple[bool, List[Dict]]:
        """Определить наличие Telegram Premium Custom Emoji в сообщении"""
        if not message:
            return False, []

        premium_emojis = []
        has_premium = False

        # Проверяем entities сообщения на наличие custom emoji
        if hasattr(message, 'entities') and message.entities:
            for entity in message.entities:
                # MessageEntityCustomEmoji - это премиум эмодзи
                if hasattr(entity, 'document_id') and entity.document_id:
                    has_premium = True
                    premium_emojis.append({
                        'document_id': entity.document_id,
                        'offset': entity.offset,
                        'length': entity.length,
                        'emoji': message.text[entity.offset:entity.offset + entity.length] if message.text else ""
                    })

        return has_premium, premium_emojis

    def get_media_type(self, media) -> Optional[str]:
        """Определить тип медиа"""
        if not media:
            return None

        if isinstance(media, MessageMediaPhoto):
            return "photo"
        elif isinstance(media, MessageMediaDocument):
            if media.document:
                for attr in media.document.attributes:
                    if isinstance(attr, DocumentAttributeVideo):
                        return "video"
                    elif isinstance(attr, DocumentAttributeAnimated):
                        return "animation"
                    elif isinstance(attr, DocumentAttributeSticker):
                        return "sticker"
                return "document"
        elif isinstance(media, MessageMediaWebPage):
            return "webpage"

        return "unknown"

    async def download_media(self, client, message, post_id: int) -> Optional[Dict]:
        """Скачать медиа файлы"""
        if not message.media:
            return None

        try:
            media_info = {
                "type": self.get_media_type(message.media),
                "files": [],
                "post_id": post_id
            }

            # Создаем папку для поста
            post_dir = self.media_dir / f"post_{post_id}"
            post_dir.mkdir(exist_ok=True)

            # Скачиваем основной файл
            if media_info["type"] in ["photo", "video", "document", "animation"]:
                file_path = await client.download_media(
                    message.media,
                    file=str(post_dir)
                )

                if file_path:
                    media_info["files"].append({
                        "path": str(file_path),
                        "type": media_info["type"],
                        "size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    })

            # Сохраняем информацию о медиа
            info_file = post_dir / "media_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(media_info, f, ensure_ascii=False, indent=2)

            return media_info

        except Exception as e:
            print(f"Error downloading media: {e}")
            return None

    def get_media_info(self, post_id: int) -> Optional[Dict]:
        """Получить информацию о медиа поста"""
        info_file = self.media_dir / f"post_{post_id}" / "media_info.json"

        if info_file.exists():
            try:
                with open(info_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error reading media info: {e}")

        return None

    def save_premium_emoji_info(self, post_id: int, premium_emojis: List[Dict]) -> bool:
        """Сохранить информацию о премиум эмодзи"""
        if not premium_emojis:
            return False

        try:
            post_dir = self.media_dir / f"post_{post_id}"
            post_dir.mkdir(exist_ok=True)

            emoji_file = post_dir / "premium_emoji.json"
            with open(emoji_file, 'w', encoding='utf-8') as f:
                json.dump(premium_emojis, f, ensure_ascii=False, indent=2)

            return True
        except Exception as e:
            print(f"Error saving premium emoji info: {e}")
            return False

    def get_premium_emoji_info(self, post_id: int) -> List[Dict]:
        """Получить информацию о премиум эмодзи поста"""
        emoji_file = self.media_dir / f"post_{post_id}" / "premium_emoji.json"

        if emoji_file.exists():
            try:
                with open(emoji_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error reading premium emoji info: {e}")

        return []

    def preserve_emoji_formatting(self, text: str, premium_emojis: List[Dict] = None) -> str:
        """Сохранить форматирование эмодзи при обработке"""
        if not text:
            return text

        # Если есть премиум эмодзи, сохраняем их позиции
        if premium_emojis:
            # Сортируем по позиции (в обратном порядке для корректной замены)
            sorted_emojis = sorted(premium_emojis, key=lambda x: x['offset'], reverse=True)

            # Помечаем премиум эмодзи специальными маркерами
            processed_text = text
            for emoji_info in sorted_emojis:
                start = emoji_info['offset']
                end = start + emoji_info['length']
                emoji_text = emoji_info['emoji']

                # Заменяем на маркер с ID документа
                marker = f"[PREMIUM_EMOJI_{emoji_info['document_id']}:{emoji_text}]"
                processed_text = processed_text[:start] + marker + processed_text[end:]

            return processed_text

        return text  # Возвращаем оригинальный текст

    async def prepare_media_for_publishing(self, post_id: int, target_channel_id: int) -> Optional[List[str]]:
        """Подготовить медиа для публикации"""
        media_info = self.get_media_info(post_id)

        if not media_info or not media_info.get("files"):
            return None

        # Возвращаем пути к файлам для публикации
        return [file_info["path"] for file_info in media_info["files"]]

    def cleanup_old_media(self, days_old: int = 7):
        """Очистить старые медиа файлы"""
        import time
        current_time = time.time()

        for post_dir in self.media_dir.iterdir():
            if post_dir.is_dir() and post_dir.name.startswith("post_"):
                # Проверяем возраст папки
                dir_age = current_time - post_dir.stat().st_mtime
                if dir_age > (days_old * 24 * 60 * 60):  # Конвертируем дни в секунды
                    try:
                        import shutil
                        shutil.rmtree(post_dir)
                        print(f"Cleaned up old media: {post_dir}")
                    except Exception as e:
                        print(f"Error cleaning up {post_dir}: {e}")

# Глобальный экземпляр обработчика медиа
media_handler = MediaHandler()
