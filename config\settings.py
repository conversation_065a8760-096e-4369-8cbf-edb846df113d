import os
from dotenv import load_dotenv
from pathlib import Path

# Загружаем переменные окружения
load_dotenv()

class Settings:
    # Telegram Bot
    BOT_TOKEN = os.getenv("BOT_TOKEN")
    
    # Telegram User Client
    API_ID = int(os.getenv("API_ID"))
    API_HASH = os.getenv("API_HASH")
    
    # AI API
    ZEROEVAL_API_KEY = os.getenv("ZEROEVAL_API_KEY")
    AI_BASE_URL = "https://api.zeroeval.com/proxy/chat/completions"
    AI_MODEL = "o3-2025-04-16"
    
    # Admin
    ADMIN_ID = int(os.getenv("ADMIN_ID", 0))
    
    # Database
    DATABASE_PATH = os.getenv("DATABASE_PATH", "./data/bot.db")
    
    # Channels
    MONITOR_CHANNELS = [ch.strip() for ch in os.getenv("MONITOR_CHANNELS", "").split(",") if ch.strip()]
    
    # Templates
    DEFAULT_AD_TEMPLATE = os.getenv("DEFAULT_AD_TEMPLATE", "🔥 Наша реклама здесь 🔥")
    
    # Создаем директории
    @classmethod
    def create_directories(cls):
        Path(cls.DATABASE_PATH).parent.mkdir(parents=True, exist_ok=True)
        Path("./logs").mkdir(exist_ok=True)
        Path("./sessions").mkdir(exist_ok=True)

settings = Settings()
