import asyncio
import json
from telethon import TelegramClient, events
from telethon.tl.types import Channel, Chat
from loguru import logger
from config.settings import settings
from database.models import db
from ai.processor import ai_processor
from utils.media_handler import media_handler

class ChannelMonitor:
    def __init__(self):
        self.client = TelegramClient(
            './sessions/monitor_session',
            settings.API_ID,
            settings.API_HASH
        )
        self.monitored_channels = set()
        self.message_queue = asyncio.Queue()

    async def start(self):
        """Запуск мониторинга каналов"""
        logger.info("Starting channel monitor...")

        await self.client.start()
        logger.info("Telegram client started")

        # Загружаем каналы для мониторинга
        await self._load_monitored_channels()

        # Регистрируем обработчик новых сообщений
        @self.client.on(events.NewMessage)
        async def handle_new_message(event):
            await self._handle_new_message(event)

        logger.info(f"Monitoring {len(self.monitored_channels)} channels")

        # Запускаем обработчик очереди сообщений
        asyncio.create_task(self._process_message_queue())

        # Держим клиент активным
        await self.client.run_until_disconnected()

    async def _load_monitored_channels(self):
        """Загрузка каналов для мониторинга из БД"""
        channels = await db.get_active_channels()
        for channel in channels:
            self.monitored_channels.add(channel.id)
            logger.info(f"Added channel to monitoring: {channel.username} ({channel.id})")

    async def add_channel(self, channel_username: str) -> bool:
        """Добавить канал для мониторинга"""
        try:
            # Получаем информацию о канале
            entity = await self.client.get_entity(channel_username)

            if isinstance(entity, (Channel, Chat)):
                # Получаем правильный chat_id для мониторинга
                chat_id = entity.id
                if hasattr(entity, 'access_hash'):
                    # Для каналов используем полный ID
                    chat_id = int(f"-100{entity.id}")

                # Добавляем в БД
                success = await db.add_channel(
                    channel_id=chat_id,
                    username=channel_username,
                    title=entity.title
                )

                if success:
                    self.monitored_channels.add(chat_id)
                    logger.info(f"Channel added: {channel_username} ({chat_id})")
                    logger.info(f"Now monitoring {len(self.monitored_channels)} channels")
                    return True

            return False

        except Exception as e:
            logger.error(f"Error adding channel {channel_username}: {e}")
            return False

    async def _handle_new_message(self, event):
        """Обработка нового сообщения"""
        try:
            logger.info(f"New message received from chat_id: {event.chat_id}")
            logger.info(f"Monitored channels: {self.monitored_channels}")

            # Проверяем, что сообщение из мониторимого канала
            if event.chat_id not in self.monitored_channels:
                logger.info(f"Message from unmonitored channel {event.chat_id}, skipping")
                return

            # Проверяем, что есть текст или медиа
            if not event.message.text and not event.message.media:
                logger.info(f"Message has no text or media, skipping")
                return

            # Определяем тип медиа и наличие премиум эмодзи
            media_type = media_handler.get_media_type(event.message.media) if event.message.media else None
            has_premium_emoji, premium_emojis = media_handler.detect_premium_emoji(event.message)

            logger.info(f"Processing message: text={bool(event.message.text)}, media={media_type}, premium_emoji={has_premium_emoji}")

            # Добавляем в очередь для обработки
            await self.message_queue.put({
                'channel_id': event.chat_id,
                'message_id': event.message.id,
                'text': event.message.text,
                'media': event.message.media,
                'media_type': media_type,
                'has_premium_emoji': has_premium_emoji,
                'premium_emojis': premium_emojis,
                'date': event.message.date,
                'message': event.message  # Полный объект сообщения для скачивания медиа
            })

            logger.info(f"New message queued from channel {event.chat_id}")

        except Exception as e:
            logger.error(f"Error handling new message: {e}")

    async def _process_message_queue(self):
        """Обработка очереди сообщений"""
        while True:
            try:
                # Получаем сообщение из очереди
                message_data = await self.message_queue.get()

                # Скачиваем медиа если есть
                media_paths = None
                if message_data.get('media') and message_data.get('message'):
                    # Временно создаем пост для получения ID
                    temp_post_id = int(asyncio.get_event_loop().time() * 1000000) % 1000000
                    media_info = await media_handler.download_media(
                        self.client,
                        message_data['message'],
                        temp_post_id
                    )
                    if media_info:
                        media_paths = json.dumps(media_info)

                # Сохраняем в БД
                post_id = await db.add_post(
                    channel_id=message_data['channel_id'],
                    message_id=message_data['message_id'],
                    text=message_data.get('text'),
                    media_paths=media_paths,
                    media_type=message_data.get('media_type'),
                    has_premium_emoji=message_data.get('has_premium_emoji', False)
                )

                # Сохраняем информацию о премиум эмодзи
                premium_emojis = message_data.get('premium_emojis', [])
                if premium_emojis and post_id:
                    media_handler.save_premium_emoji_info(post_id, premium_emojis)

                # Обрабатываем текст с помощью ИИ (если есть)
                processed_text = None
                if message_data.get('text'):
                    # Сохраняем форматирование эмодзи
                    preserved_text = media_handler.preserve_emoji_formatting(
                        message_data['text'],
                        premium_emojis
                    )
                    processed_text = await ai_processor.process_text(preserved_text)

                if processed_text:
                    # Сохраняем обработанный текст
                    await db.update_post_processed(post_id, processed_text)

                # Отправляем на модерацию (через бота)
                await self._send_for_moderation(
                    post_id,
                    message_data.get('text', '[Медиа без текста]'),
                    processed_text or '[Обработка не требуется]',
                    message_data.get('media_type'),
                    message_data.get('has_premium_emoji', False)
                )

                logger.info(f"Message processed: post_id={post_id}, media={message_data.get('media_type')}")

            except Exception as e:
                logger.error(f"Error processing message queue: {e}")
                await asyncio.sleep(1)

    async def _send_for_moderation(self, post_id: int, original_text: str, processed_text: str,
                                  media_type: str = None, has_premium_emoji: bool = False):
        """Отправка поста на модерацию (через файл-очередь)"""
        try:
            moderation_data = {
                'post_id': post_id,
                'original_text': original_text,
                'processed_text': processed_text,
                'media_type': media_type,
                'has_premium_emoji': has_premium_emoji,
                'timestamp': str(asyncio.get_event_loop().time())
            }

            # Записываем в файл для передачи боту
            with open('./data/moderation_queue.jsonl', 'a', encoding='utf-8') as f:
                f.write(json.dumps(moderation_data, ensure_ascii=False) + '\n')

            logger.info(f"Post {post_id} sent for moderation (media: {media_type}, premium_emoji: {has_premium_emoji})")

        except Exception as e:
            logger.error(f"Error sending post for moderation: {e}")

# Глобальный экземпляр монитора
monitor = ChannelMonitor()
