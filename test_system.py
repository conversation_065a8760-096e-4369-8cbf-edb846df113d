#!/usr/bin/env python3
"""
Скрипт для тестирования системы мониторинга
"""

import asyncio
import aiosqlite
from config.settings import settings

async def check_database():
    """Проверить содержимое базы данных"""
    print("🔍 Проверяю базу данных...")
    
    try:
        async with aiosqlite.connect(settings.DATABASE_PATH) as db:
            # Проверяем каналы-доноры
            async with db.execute("SELECT * FROM channels") as cursor:
                donors = await cursor.fetchall()
                print(f"📢 Каналы-доноры: {len(donors)}")
                for donor in donors:
                    print(f"   - {donor[2]} (@{donor[1]}) ID: {donor[0]}")
            
            # Проверяем целевые каналы
            async with db.execute("SELECT * FROM target_channels") as cursor:
                targets = await cursor.fetchall()
                print(f"🎯 Целевые каналы: {len(targets)}")
                for target in targets:
                    print(f"   - {target[2]} (@{target[1]}) ID: {target[0]}")
            
            # Проверяем маппинги
            async with db.execute("SELECT * FROM channel_mappings") as cursor:
                mappings = await cursor.fetchall()
                print(f"🔗 Связи каналов: {len(mappings)}")
                for mapping in mappings:
                    print(f"   - Донор ID: {mapping[1]} → Цель ID: {mapping[2]}")
            
            # Проверяем посты
            async with db.execute("SELECT * FROM posts") as cursor:
                posts = await cursor.fetchall()
                print(f"📝 Посты: {len(posts)}")
                for post in posts:
                    print(f"   - Пост #{post[0]} от канала {post[1]} статус: {post[6]}")
                    
    except Exception as e:
        print(f"❌ Ошибка при проверке БД: {e}")

async def check_files():
    """Проверить файлы системы"""
    print("\n📁 Проверяю файлы...")
    
    import os
    
    files_to_check = [
        settings.DATABASE_PATH,
        "./data/moderation_queue.jsonl",
        "./logs/bot.log"
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} байт)")
        else:
            print(f"❌ {file_path} не найден")

async def main():
    """Главная функция"""
    print("🔧 Диагностика системы Post Thief Bot\n")
    
    # Проверяем настройки
    print("⚙️ Настройки:")
    print(f"   BOT_TOKEN: {'✅ Установлен' if settings.BOT_TOKEN else '❌ Не установлен'}")
    print(f"   API_ID: {'✅ Установлен' if settings.API_ID else '❌ Не установлен'}")
    print(f"   API_HASH: {'✅ Установлен' if settings.API_HASH else '❌ Не установлен'}")
    print(f"   ADMIN_ID: {settings.ADMIN_ID}")
    print(f"   DATABASE_PATH: {settings.DATABASE_PATH}")
    print()
    
    await check_files()
    await check_database()
    
    print("\n💡 Рекомендации:")
    print("1. Убедитесь, что система запущена: python main.py")
    print("2. Проверьте, что каналы добавлены: /donors и /targets")
    print("3. Убедитесь, что есть связи: /mappings")
    print("4. Перезагрузите мониторинг: /reload_monitoring")
    print("5. Проверьте логи: logs/bot.log")

if __name__ == "__main__":
    asyncio.run(main())
