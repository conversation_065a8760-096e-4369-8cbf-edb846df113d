# 🤖 Post Thief Bot - Бот-вор постов для Telegram

Автоматическая система для мониторинга Telegram каналов, уникализации контента с помощью ИИ и публикации с заменой рекламы.

## 🎯 Возможности

- **Мониторинг каналов**: Отслеживание новых постов в указанных каналах
- **ИИ обработка**: Уникализация текста и замена рекламы
- **Модерация**: Ручное одобрение постов перед публикацией
- **Автоматизация**: Полный цикл от мониторинга до публикации

## 🏗️ Архитектура

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Telegram      │    │   AI Service    │    │   Telegram      │
│   User Client   │───▶│   (ZeroEval)    │───▶│   Bot           │
│   (Monitor)     │    │   Text Process  │    │   (Moderation)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Channels      │    │   Database      │    │   Admin         │
│   Monitoring    │    │   (SQLite)      │    │   Interface     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 2. Настройка конфигурации

Отредактируйте файл `.env`:

```env
# Telegram Bot API (получить у @BotFather)
BOT_TOKEN=8154816134:AAHsoim6iOysQ6_VJQI0plUE0S9Mx4jewR4

# Telegram User Client API (получить на https://my.telegram.org)
API_ID=13434740
API_HASH=ddcb8777d24aa5a651a5bab7e17c585d

# AI API (ZeroEval)
ZEROEVAL_API_KEY=8154816134:AAHsoim6iOysQ6_VJQI0plUE0S9Mx4jewR4

# Ваш Telegram ID (получить у @userinfobot)
ADMIN_ID=YOUR_TELEGRAM_ID_HERE

# Шаблон рекламы
DEFAULT_AD_TEMPLATE=🔥 Наша реклама здесь 🔥
```

### 3. Запуск

**Windows:**
```bash
start.bat
```

**Linux/Mac:**
```bash
python main.py
```

## 📱 Использование

### Команды бота

- `/start` - Запуск бота
- `/add_channel` - Добавить канал для мониторинга
- `/channels` - Список мониторимых каналов
- `/stats` - Статистика постов
- `/help` - Справка

### Процесс работы

1. **Добавление каналов**: Используйте `/add_channel` для добавления каналов
2. **Автоматический мониторинг**: Бот отслеживает новые посты
3. **ИИ обработка**: Каждый пост уникализируется и очищается от рекламы
4. **Модерация**: Обработанные посты отправляются вам на одобрение
5. **Публикация**: Одобренные посты публикуются

## ⚙️ Настройка

### Получение API ключей

1. **Bot Token**: 
   - Напишите @BotFather в Telegram
   - Создайте нового бота командой `/newbot`
   - Скопируйте полученный токен

2. **API ID и API Hash**:
   - Перейдите на https://my.telegram.org
   - Войдите в аккаунт
   - Создайте приложение в разделе "API development tools"
   - Скопируйте API ID и API Hash

3. **Ваш Telegram ID**:
   - Напишите @userinfobot в Telegram
   - Отправьте любое сообщение
   - Скопируйте ваш ID

### Важные моменты

- ⚠️ Ваш аккаунт должен быть подписан на все мониторимые каналы
- 🔐 Храните API ключи в безопасности
- 📝 Бот работает только с текстовыми постами
- 🤖 Все посты проходят через ИИ для уникализации

## 📁 Структура проекта

```
├── main.py                 # Главный файл запуска
├── requirements.txt        # Зависимости
├── .env                   # Конфигурация
├── start.bat              # Скрипт запуска для Windows
├── config/                # Настройки
│   └── settings.py
├── database/              # База данных
│   └── models.py
├── monitor/               # Мониторинг каналов
│   └── channel_monitor.py
├── ai/                    # ИИ обработка
│   └── processor.py
├── bot/                   # Telegram бот
│   ├── main.py
│   ├── handlers.py
│   └── moderation.py
├── data/                  # Данные (создается автоматически)
├── logs/                  # Логи (создается автоматически)
└── sessions/              # Сессии Telegram (создается автоматически)
```

## 🔧 Расширенная настройка

### Шаблоны рекламы

Вы можете настроить шаблон рекламы в файле `.env`:

```env
DEFAULT_AD_TEMPLATE=🔥 Подписывайтесь на наш канал: @your_channel 🔥
```

### Настройка ИИ

Параметры обработки текста можно изменить в файле `ai/processor.py`:

- `temperature` - креативность (0.1-1.0)
- `max_tokens` - максимальная длина ответа
- Промпт для уникализации

## 🐛 Решение проблем

### Частые ошибки

1. **"Configuration error"**
   - Проверьте все API ключи в `.env`
   - Убедитесь, что файл `.env` находится в корне проекта

2. **"Error adding channel"**
   - Проверьте, что вы подписаны на канал
   - Убедитесь в правильности username канала
   - Канал должен быть публичным или вы должны быть его участником

3. **"AI processing error"**
   - Проверьте API ключ ZeroEval
   - Убедитесь в наличии интернет-соединения

### Логи

Логи сохраняются в папке `logs/`:
- `bot.log` - подробные логи работы системы

## 📞 Поддержка

При возникновении проблем:

1. Проверьте логи в папке `logs/`
2. Убедитесь в правильности настроек в `.env`
3. Проверьте подключение к интернету
4. Убедитесь, что все зависимости установлены

## 📄 Лицензия

Этот проект предназначен для образовательных целей. Используйте ответственно и соблюдайте правила Telegram.
