import json
import os
import asyncio
from aiogram import Router, F
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.filters import Command, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from loguru import logger

from config.settings import settings
from database.models import db
from monitor.channel_monitor import monitor

router = Router()

class ChannelStates(StatesGroup):
    waiting_for_channel = State()

@router.message(Command("start"))
async def cmd_start(message: Message):
    """Команда /start"""
    if message.from_user.id != settings.ADMIN_ID:
        await message.answer("❌ У вас нет доступа к этому боту")
        return
    
    await message.answer(
        "🤖 <b>Бот-вор постов запущен!</b>\n\n"
        "📋 <b>Доступные команды:</b>\n"
        "/add_channel - Добавить канал для мониторинга\n"
        "/channels - Список мониторимых каналов\n"
        "/stats - Статистика постов\n"
        "/help - Помощь",
        parse_mode="HTML"
    )

@router.message(Command("add_channel"))
async def cmd_add_channel(message: Message, state: FSMContext):
    """Добавить канал для мониторинга"""
    if message.from_user.id != settings.ADMIN_ID:
        return
    
    await message.answer(
        "📢 Отправьте username канала для мониторинга\n"
        "Формат: @channel_name или channel_name\n\n"
        "❗️ Важно: ваш аккаунт должен быть подписан на этот канал!"
    )
    await state.set_state(ChannelStates.waiting_for_channel)

@router.message(StateFilter(ChannelStates.waiting_for_channel))
async def process_channel_username(message: Message, state: FSMContext):
    """Обработка username канала"""
    channel_username = message.text.strip()
    
    # Очищаем от @ если есть
    if channel_username.startswith('@'):
        channel_username = channel_username[1:]
    
    await message.answer("⏳ Добавляю канал...")
    
    # Добавляем канал через монитор
    success = await monitor.add_channel(channel_username)
    
    if success:
        await message.answer(
            f"✅ Канал @{channel_username} успешно добавлен для мониторинга!"
        )
    else:
        await message.answer(
            f"❌ Не удалось добавить канал @{channel_username}\n"
            "Проверьте:\n"
            "• Правильность username\n"
            "• Подписку на канал\n"
            "• Доступность канала"
        )
    
    await state.clear()

@router.message(Command("channels"))
async def cmd_channels(message: Message):
    """Список мониторимых каналов"""
    if message.from_user.id != settings.ADMIN_ID:
        return
    
    channels = await db.get_active_channels()
    
    if not channels:
        await message.answer("📭 Нет каналов для мониторинга")
        return
    
    text = "📢 <b>Мониторимые каналы:</b>\n\n"
    for channel in channels:
        status = "🟢" if channel.is_active else "🔴"
        text += f"{status} <b>{channel.title}</b>\n"
        text += f"   @{channel.username}\n"
        text += f"   ID: {channel.id}\n\n"
    
    await message.answer(text, parse_mode="HTML")

@router.message(Command("stats"))
async def cmd_stats(message: Message):
    """Статистика постов"""
    if message.from_user.id != settings.ADMIN_ID:
        return
    
    pending_posts = await db.get_pending_posts()
    
    text = "📊 <b>Статистика:</b>\n\n"
    text += f"⏳ Ожидают модерации: {len(pending_posts)}\n"
    
    await message.answer(text, parse_mode="HTML")

@router.callback_query(F.data.startswith("approve_"))
async def approve_post(callback: CallbackQuery):
    """Одобрить пост"""
    post_id = int(callback.data.split("_")[1])
    
    # Обновляем статус в БД
    await db.update_post_status(post_id, "approved")
    
    # Получаем пост для публикации
    post = await db.get_post(post_id)
    
    if post and post.processed_text:
        # Здесь должна быть логика публикации в целевой канал
        # Пока просто отправляем подтверждение
        await callback.message.edit_text(
            f"✅ <b>Пост одобрен и опубликован!</b>\n\n"
            f"📝 <b>Текст:</b>\n{post.processed_text[:200]}...",
            parse_mode="HTML"
        )
        
        logger.info(f"Post {post_id} approved and published")
    
    await callback.answer("✅ Пост одобрен!")

@router.callback_query(F.data.startswith("reject_"))
async def reject_post(callback: CallbackQuery):
    """Отклонить пост"""
    post_id = int(callback.data.split("_")[1])
    
    # Обновляем статус в БД
    await db.update_post_status(post_id, "rejected")
    
    await callback.message.edit_text(
        "❌ <b>Пост отклонен</b>",
        parse_mode="HTML"
    )
    
    await callback.answer("❌ Пост отклонен!")
    logger.info(f"Post {post_id} rejected")

@router.message(Command("help"))
async def cmd_help(message: Message):
    """Помощь"""
    if message.from_user.id != settings.ADMIN_ID:
        return
    
    help_text = """
🤖 <b>Бот-вор постов</b>

<b>Как работает:</b>
1. Добавьте каналы для мониторинга командой /add_channel
2. Бот будет отслеживать новые посты в этих каналах
3. Каждый пост обрабатывается ИИ (уникализация + замена рекламы)
4. Обработанный пост отправляется вам на модерацию
5. Вы одобряете или отклоняете пост
6. Одобренные посты публикуются

<b>Команды:</b>
/add_channel - Добавить канал для мониторинга
/channels - Список мониторимых каналов  
/stats - Статистика постов
/help - Эта справка

<b>Важно:</b>
• Ваш аккаунт должен быть подписан на мониторимые каналы
• Бот работает только с текстовыми постами
• Все посты проходят через ИИ для уникализации
"""
    
    await message.answer(help_text, parse_mode="HTML")
