import json
import os
import asyncio
from aiogram import Router, F
from aiogram.types import Message, CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.filters import Command, StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup
from loguru import logger

from config.settings import settings
from database.models import db
from monitor.channel_monitor import monitor

router = Router()

class ChannelStates(StatesGroup):
    waiting_for_donor_channel = State()
    waiting_for_target_channel = State()
    waiting_for_ad_template = State()
    waiting_for_mapping_donor = State()
    waiting_for_mapping_target = State()

@router.message(Command("start"))
async def cmd_start(message: Message):
    """Команда /start"""
    if message.from_user.id != settings.ADMIN_ID:
        await message.answer("❌ У вас нет доступа к этому боту")
        return

    await message.answer(
        "🤖 <b>Бот-вор постов запущен!</b>\n\n"
        "📋 <b>Управление каналами:</b>\n"
        "/add_donor - Добавить канал-донор для мониторинга\n"
        "/add_target - Добавить целевой канал для публикации\n"
        "/link_channels - Связать донора с целевым каналом\n"
        "/donors - Список каналов-доноров\n"
        "/targets - Список целевых каналов\n"
        "/mappings - Показать все связи каналов\n\n"
        "📊 <b>Статистика и управление:</b>\n"
        "/stats - Статистика постов\n"
        "/pending - Посты на модерации\n"
        "/reload_monitoring - Перезагрузить мониторинг\n"
        "/templates - Управление шаблонами рекламы\n\n"
        "ℹ️ <b>Справка:</b>\n"
        "/help - Подробная помощь",
        parse_mode="HTML"
    )

@router.message(Command("add_donor"))
async def cmd_add_donor(message: Message, state: FSMContext):
    """Добавить канал-донор для мониторинга"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    await message.answer(
        "📢 <b>Добавление канала-донора</b>\n\n"
        "Отправьте username канала для мониторинга\n"
        "Формат: @channel_name или channel_name\n\n"
        "❗️ <b>Важно:</b> ваш аккаунт должен быть подписан на этот канал!",
        parse_mode="HTML"
    )
    await state.set_state(ChannelStates.waiting_for_donor_channel)

@router.message(StateFilter(ChannelStates.waiting_for_donor_channel))
async def process_donor_channel(message: Message, state: FSMContext):
    """Обработка username канала-донора"""
    channel_username = message.text.strip()

    # Очищаем от @ если есть
    if channel_username.startswith('@'):
        channel_username = channel_username[1:]

    await message.answer("⏳ Добавляю канал-донор...")

    # Добавляем канал через монитор
    success = await monitor.add_channel(channel_username)

    if success:
        await message.answer(
            f"✅ Канал-донор @{channel_username} успешно добавлен для мониторинга!\n\n"
            f"💡 Теперь используйте /add_target для добавления целевых каналов\n"
            f"и /link_channels для связывания каналов",
            parse_mode="HTML"
        )
    else:
        await message.answer(
            f"❌ Не удалось добавить канал @{channel_username}\n\n"
            "<b>Проверьте:</b>\n"
            "• Правильность username\n"
            "• Подписку на канал\n"
            "• Доступность канала",
            parse_mode="HTML"
        )

    await state.clear()

@router.message(Command("add_target"))
async def cmd_add_target(message: Message, state: FSMContext):
    """Добавить целевой канал для публикации"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    await message.answer(
        "🎯 <b>Добавление целевого канала</b>\n\n"
        "Отправьте username канала для публикации постов\n"
        "Формат: @channel_name или channel_name\n\n"
        "❗️ <b>Важно:</b> бот должен быть администратором этого канала!",
        parse_mode="HTML"
    )
    await state.set_state(ChannelStates.waiting_for_target_channel)

@router.message(StateFilter(ChannelStates.waiting_for_target_channel))
async def process_target_channel(message: Message, state: FSMContext):
    """Обработка username целевого канала"""
    channel_username = message.text.strip()

    # Очищаем от @ если есть
    if channel_username.startswith('@'):
        channel_username = channel_username[1:]

    await message.answer("⏳ Добавляю целевой канал...")

    try:
        # Получаем информацию о канале через монитор
        entity = await monitor.client.get_entity(channel_username)

        # Добавляем в БД как целевой канал
        success = await db.add_target_channel(
            channel_id=entity.id,
            username=channel_username,
            title=entity.title
        )

        if success:
            await state.update_data(target_channel_id=entity.id, target_username=channel_username)
            await message.answer(
                f"✅ Целевой канал @{channel_username} добавлен!\n\n"
                f"📝 Хотите установить индивидуальный шаблон рекламы для этого канала?\n"
                f"Отправьте текст шаблона или /skip для использования стандартного",
                parse_mode="HTML"
            )
            await state.set_state(ChannelStates.waiting_for_ad_template)
        else:
            await message.answer("❌ Ошибка при добавлении канала в базу данных")
            await state.clear()

    except Exception as e:
        await message.answer(
            f"❌ Не удалось добавить канал @{channel_username}\n\n"
            f"<b>Ошибка:</b> {str(e)}\n\n"
            "<b>Проверьте:</b>\n"
            "• Правильность username\n"
            "• Права бота в канале\n"
            "• Доступность канала",
            parse_mode="HTML"
        )
        await state.clear()

@router.message(StateFilter(ChannelStates.waiting_for_ad_template))
async def process_ad_template(message: Message, state: FSMContext):
    """Обработка шаблона рекламы"""
    data = await state.get_data()
    target_channel_id = data.get('target_channel_id')
    target_username = data.get('target_username')

    if message.text.strip() == "/skip":
        ad_template = None
        template_text = "стандартный шаблон"
    else:
        ad_template = message.text.strip()
        template_text = f"индивидуальный шаблон:\n<code>{ad_template}</code>"

    # Обновляем шаблон в БД
    try:
        import aiosqlite
        async with aiosqlite.connect(db.db_path) as conn:
            await conn.execute(
                "UPDATE target_channels SET ad_template = ? WHERE id = ?",
                (ad_template, target_channel_id)
            )
            await conn.commit()

        await message.answer(
            f"✅ Настройка завершена!\n\n"
            f"🎯 <b>Канал:</b> @{target_username}\n"
            f"📝 <b>Шаблон рекламы:</b> {template_text}\n\n"
            f"💡 Используйте /link_channels для связывания с каналами-донорами",
            parse_mode="HTML"
        )

    except Exception as e:
        await message.answer(f"❌ Ошибка при сохранении шаблона: {str(e)}")

    await state.clear()

@router.message(Command("donors"))
async def cmd_donors(message: Message):
    """Список каналов-доноров"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    channels = await db.get_active_channels()

    if not channels:
        await message.answer("📭 Нет каналов-доноров для мониторинга")
        return

    text = "📢 <b>Каналы-доноры:</b>\n\n"
    for channel in channels:
        status = "🟢" if channel.is_active else "🔴"
        text += f"{status} <b>{channel.title}</b>\n"
        text += f"   @{channel.username}\n"
        text += f"   ID: {channel.id}\n\n"

    await message.answer(text, parse_mode="HTML")

@router.message(Command("targets"))
async def cmd_targets(message: Message):
    """Список целевых каналов"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    channels = await db.get_target_channels()

    if not channels:
        await message.answer("📭 Нет целевых каналов для публикации")
        return

    text = "🎯 <b>Целевые каналы:</b>\n\n"
    for channel in channels:
        status = "🟢" if channel.is_active else "🔴"
        text += f"{status} <b>{channel.title}</b>\n"
        text += f"   @{channel.username}\n"
        text += f"   ID: {channel.id}\n"
        if channel.ad_template:
            text += f"   📝 Реклама: {channel.ad_template[:50]}...\n"
        else:
            text += f"   📝 Реклама: стандартная\n"
        text += "\n"

    await message.answer(text, parse_mode="HTML")

@router.message(Command("reload_monitoring"))
async def cmd_reload_monitoring(message: Message):
    """Перезагрузить мониторинг каналов"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    await message.answer("🔄 Перезагружаю мониторинг каналов...")

    # Перезагружаем каналы в мониторе
    await monitor._load_monitored_channels()

    channels_count = len(monitor.monitored_channels)
    await message.answer(
        f"✅ Мониторинг перезагружен!\n"
        f"📡 Отслеживается каналов: {channels_count}",
        parse_mode="HTML"
    )

@router.message(Command("link_channels"))
async def cmd_link_channels(message: Message, state: FSMContext):
    """Связать канал-донор с целевым каналом"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    # Получаем список доноров
    donors = await db.get_active_channels()
    if not donors:
        await message.answer("❌ Сначала добавьте каналы-доноры командой /add_donor")
        return

    text = "🔗 <b>Связывание каналов</b>\n\n"
    text += "Выберите канал-донор (отправьте номер):\n\n"

    for i, donor in enumerate(donors, 1):
        text += f"{i}. <b>{donor.title}</b> (@{donor.username})\n"

    await state.update_data(donors=donors)
    await message.answer(text, parse_mode="HTML")
    await state.set_state(ChannelStates.waiting_for_mapping_donor)

@router.message(StateFilter(ChannelStates.waiting_for_mapping_donor))
async def process_mapping_donor(message: Message, state: FSMContext):
    """Обработка выбора канала-донора"""
    try:
        donor_index = int(message.text.strip()) - 1
        data = await state.get_data()
        donors = data.get('donors', [])

        if 0 <= donor_index < len(donors):
            selected_donor = donors[donor_index]

            # Получаем список целевых каналов
            targets = await db.get_target_channels()
            if not targets:
                await message.answer("❌ Сначала добавьте целевые каналы командой /add_target")
                await state.clear()
                return

            text = f"✅ Выбран донор: <b>{selected_donor.title}</b>\n\n"
            text += "Теперь выберите целевой канал (отправьте номер):\n\n"

            for i, target in enumerate(targets, 1):
                text += f"{i}. <b>{target.title}</b> (@{target.username})\n"

            await state.update_data(selected_donor=selected_donor, targets=targets)
            await message.answer(text, parse_mode="HTML")
            await state.set_state(ChannelStates.waiting_for_mapping_target)
        else:
            await message.answer("❌ Неверный номер. Попробуйте еще раз.")
    except ValueError:
        await message.answer("❌ Отправьте номер канала из списка.")

@router.message(StateFilter(ChannelStates.waiting_for_mapping_target))
async def process_mapping_target(message: Message, state: FSMContext):
    """Обработка выбора целевого канала"""
    try:
        target_index = int(message.text.strip()) - 1
        data = await state.get_data()
        targets = data.get('targets', [])
        selected_donor = data.get('selected_donor')

        if 0 <= target_index < len(targets):
            selected_target = targets[target_index]

            # Создаем связь
            success = await db.add_channel_mapping(selected_donor.id, selected_target.id)

            if success:
                await message.answer(
                    f"✅ <b>Связь создана!</b>\n\n"
                    f"📢 <b>Донор:</b> {selected_donor.title} (@{selected_donor.username})\n"
                    f"🎯 <b>Цель:</b> {selected_target.title} (@{selected_target.username})\n\n"
                    f"Теперь посты из канала-донора будут обрабатываться и отправляться в целевой канал!",
                    parse_mode="HTML"
                )
            else:
                await message.answer("❌ Ошибка при создании связи. Возможно, она уже существует.")
        else:
            await message.answer("❌ Неверный номер. Попробуйте еще раз.")
            return
    except ValueError:
        await message.answer("❌ Отправьте номер канала из списка.")
        return

    await state.clear()

@router.message(Command("mappings"))
async def cmd_mappings(message: Message):
    """Показать все связи каналов"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    mappings = await db.get_all_mappings()

    if not mappings:
        await message.answer(
            "📭 Нет активных связей между каналами\n\n"
            "💡 Используйте /link_channels для создания связей"
        )
        return

    text = "🔗 <b>Активные связи каналов:</b>\n\n"

    for i, mapping in enumerate(mappings, 1):
        text += f"{i}. <b>{mapping['donor_title']}</b>\n"
        text += f"   📢 Донор: @{mapping['donor_username']}\n"
        text += f"   🎯 Цель: @{mapping['target_username']}\n"
        text += f"   📅 Создано: {mapping['created_date'][:10]}\n\n"

    await message.answer(text, parse_mode="HTML")

@router.message(Command("stats"))
async def cmd_stats(message: Message):
    """Статистика постов"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    pending_posts = await db.get_pending_posts()

    # Получаем общую статистику
    import aiosqlite
    async with aiosqlite.connect(db.db_path) as conn:
        # Общее количество постов
        cursor = await conn.execute("SELECT COUNT(*) FROM posts")
        total_posts = (await cursor.fetchone())[0]

        # Одобренные посты
        cursor = await conn.execute("SELECT COUNT(*) FROM posts WHERE status = 'approved'")
        approved_posts = (await cursor.fetchone())[0]

        # Отклоненные посты
        cursor = await conn.execute("SELECT COUNT(*) FROM posts WHERE status = 'rejected'")
        rejected_posts = (await cursor.fetchone())[0]

        # Опубликованные посты
        cursor = await conn.execute("SELECT COUNT(*) FROM posts WHERE status = 'published'")
        published_posts = (await cursor.fetchone())[0]

    text = "📊 <b>Статистика постов:</b>\n\n"
    text += f"📝 Всего постов: {total_posts}\n"
    text += f"⏳ Ожидают модерации: {len(pending_posts)}\n"
    text += f"✅ Одобрено: {approved_posts}\n"
    text += f"📤 Опубликовано: {published_posts}\n"
    text += f"❌ Отклонено: {rejected_posts}\n\n"

    # Статистика по каналам
    donors_count = len(await db.get_active_channels())
    targets_count = len(await db.get_target_channels())
    mappings_count = len(await db.get_all_mappings())

    text += f"📢 Каналов-доноров: {donors_count}\n"
    text += f"🎯 Целевых каналов: {targets_count}\n"
    text += f"🔗 Активных связей: {mappings_count}\n"

    await message.answer(text, parse_mode="HTML")

@router.message(Command("pending"))
async def cmd_pending(message: Message):
    """Посты на модерации"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    pending_posts = await db.get_pending_posts()

    if not pending_posts:
        await message.answer("📭 Нет постов ожидающих модерации")
        return

    text = f"⏳ <b>Посты на модерации ({len(pending_posts)}):</b>\n\n"

    for i, post in enumerate(pending_posts[:5], 1):  # Показываем только первые 5
        text += f"{i}. <b>Пост #{post.id}</b>\n"
        text += f"   📅 {post.created_date[:16] if post.created_date else 'Неизвестно'}\n"
        text += f"   📝 {post.original_text[:100]}...\n\n"

    if len(pending_posts) > 5:
        text += f"... и еще {len(pending_posts) - 5} постов\n\n"

    text += "💡 Посты будут автоматически отправлены на модерацию"

    await message.answer(text, parse_mode="HTML")

@router.callback_query(F.data.startswith("approve_"))
async def approve_post(callback: CallbackQuery):
    """Одобрить пост"""
    post_id = int(callback.data.split("_")[1])

    # Обновляем статус в БД
    await db.update_post_status(post_id, "approved")

    # Получаем пост для публикации
    post = await db.get_post(post_id)

    if post and post.processed_text:
        # Получаем целевые каналы для этого донора
        target_channels = await db.get_target_channels_for_donor(post.channel_id)

        if target_channels:
            published_count = 0
            for target in target_channels:
                try:
                    # Здесь будет логика публикации в целевой канал
                    # Пока просто помечаем как опубликованный
                    published_count += 1
                except Exception as e:
                    print(f"Error publishing to {target.username}: {e}")

            # Обновляем статус на опубликованный
            await db.update_post_status(post_id, "published")

            await callback.message.edit_text(
                f"✅ <b>Пост одобрен и опубликован!</b>\n\n"
                f"📤 Опубликовано в {published_count} каналах\n"
                f"📝 <b>Текст:</b>\n{post.processed_text[:200]}...",
                parse_mode="HTML"
            )
        else:
            await callback.message.edit_text(
                f"✅ <b>Пост одобрен!</b>\n\n"
                f"⚠️ Нет связанных целевых каналов для публикации\n"
                f"📝 <b>Текст:</b>\n{post.processed_text[:200]}...",
                parse_mode="HTML"
            )

    await callback.answer("✅ Пост одобрен!")

@router.callback_query(F.data.startswith("reject_"))
async def reject_post(callback: CallbackQuery):
    """Отклонить пост"""
    post_id = int(callback.data.split("_")[1])

    # Обновляем статус в БД
    await db.update_post_status(post_id, "rejected")

    await callback.message.edit_text(
        "❌ <b>Пост отклонен</b>",
        parse_mode="HTML"
    )

    await callback.answer("❌ Пост отклонен!")

@router.message(Command("help"))
async def cmd_help(message: Message):
    """Помощь"""
    if message.from_user.id != settings.ADMIN_ID:
        return

    help_text = """
🤖 <b>Бот-вор постов - Подробная справка</b>

<b>🔄 Как работает система:</b>
1. Добавляете каналы-доноры для мониторинга
2. Добавляете целевые каналы для публикации
3. Связываете доноров с целевыми каналами
4. Система мониторит новые посты в каналах-донорах
5. ИИ обрабатывает каждый пост (уникализация + замена рекламы)
6. Обработанные посты отправляются на модерацию
7. Одобренные посты публикуются в связанных целевых каналах

<b>📋 Управление каналами:</b>
/add_donor - Добавить канал-донор для мониторинга
/add_target - Добавить целевой канал для публикации
/link_channels - Связать донора с целевым каналом
/donors - Список каналов-доноров
/targets - Список целевых каналов
/mappings - Показать все связи каналов

<b>📊 Статистика и управление:</b>
/stats - Подробная статистика постов и каналов
/pending - Посты ожидающие модерации
/templates - Управление шаблонами рекламы

<b>⚠️ Важные требования:</b>
• Ваш аккаунт должен быть подписан на все каналы-доноры
• Бот должен быть администратором всех целевых каналов
• Система работает только с текстовыми постами
• Все посты проходят через ИИ для уникализации

<b>💡 Пример настройки:</b>
1. /add_donor - добавить @news_channel
2. /add_target - добавить @my_channel
3. /link_channels - связать их
4. Готово! Посты из @news_channel будут обрабатываться и публиковаться в @my_channel

<b>🆘 Поддержка:</b>
Если что-то не работает, проверьте /stats и убедитесь, что все каналы правильно настроены.
"""

    await message.answer(help_text, parse_mode="HTML")
