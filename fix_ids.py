#!/usr/bin/env python3
import asyncio
import aiosqlite

async def fix_channel_ids():
    """Исправить ID каналов в базе данных"""
    print("🔧 Исправляю ID каналов...")
    
    try:
        async with aiosqlite.connect('./data/bot.db') as conn:
            # Показываем текущие данные
            print("\n📋 Текущие данные:")
            async with conn.execute("SELECT * FROM channels") as cursor:
                channels = await cursor.fetchall()
                for ch in channels:
                    print(f"   Канал: {ch[2]} (@{ch[1]}) ID: {ch[0]}")
            
            # Обновляем ID канала с 1530551762 на -1001530551762
            result = await conn.execute(
                "UPDATE channels SET id = ? WHERE id = ?",
                (-1001530551762, 1530551762)
            )
            print(f"✅ Обновлено каналов: {result.rowcount}")
            
            # Обновляем маппинги
            result = await conn.execute(
                "UPDATE channel_mappings SET donor_channel_id = ? WHERE donor_channel_id = ?",
                (-1001530551762, 1530551762)
            )
            print(f"✅ Обновлено маппингов: {result.rowcount}")
            
            await conn.commit()
            
            # Показываем обновленные данные
            print("\n📋 Обновленные данные:")
            async with conn.execute("SELECT * FROM channels") as cursor:
                channels = await cursor.fetchall()
                for ch in channels:
                    print(f"   Канал: {ch[2]} (@{ch[1]}) ID: {ch[0]}")
            
            print("\n✅ ID каналов успешно исправлены!")
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")

if __name__ == "__main__":
    asyncio.run(fix_channel_ids())
