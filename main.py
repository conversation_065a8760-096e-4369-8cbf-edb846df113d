import asyncio
import signal
import sys
from loguru import logger
from pathlib import Path

from config.settings import settings
from bot.main import telegram_bot
from monitor.channel_monitor import monitor

class PostThiefBot:
    def __init__(self):
        self.running = False
        self.tasks = []
    
    async def start(self):
        """Запуск всей системы"""
        logger.info("🚀 Starting Post Thief Bot system...")
        
        # Создаем необходимые директории
        settings.create_directories()
        
        # Проверяем настройки
        if not self._check_settings():
            logger.error("❌ Configuration error. Please check .env file")
            return
        
        self.running = True
        
        try:
            # Запускаем компоненты системы
            logger.info("📡 Starting channel monitor...")
            monitor_task = asyncio.create_task(monitor.start())
            self.tasks.append(monitor_task)
            
            logger.info("🤖 Starting Telegram bot...")
            bot_task = asyncio.create_task(telegram_bot.start())
            self.tasks.append(bot_task)
            
            # Ждем завершения всех задач
            await asyncio.gather(*self.tasks, return_exceptions=True)
            
        except KeyboardInterrupt:
            logger.info("🛑 Received interrupt signal")
        except Exception as e:
            logger.error(f"❌ System error: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """Остановка системы"""
        if not self.running:
            return
            
        logger.info("🛑 Stopping Post Thief Bot system...")
        self.running = False
        
        # Отменяем все задачи
        for task in self.tasks:
            if not task.done():
                task.cancel()
        
        # Ждем завершения задач
        if self.tasks:
            await asyncio.gather(*self.tasks, return_exceptions=True)
        
        # Останавливаем бота
        await telegram_bot.stop()
        
        logger.info("✅ System stopped")
    
    def _check_settings(self) -> bool:
        """Проверка настроек"""
        required_settings = [
            ('BOT_TOKEN', settings.BOT_TOKEN),
            ('API_ID', settings.API_ID),
            ('API_HASH', settings.API_HASH),
            ('ZEROEVAL_API_KEY', settings.ZEROEVAL_API_KEY),
        ]
        
        missing = []
        for name, value in required_settings:
            if not value:
                missing.append(name)
        
        if missing:
            logger.error(f"❌ Missing required settings: {', '.join(missing)}")
            logger.error("Please check your .env file")
            return False
        
        if settings.ADMIN_ID == 0:
            logger.warning("⚠️  ADMIN_ID not set. Please set your Telegram ID in .env file")
            logger.warning("You can get your ID by sending /start to @userinfobot")
        
        return True

def setup_logging():
    """Настройка логирования"""
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    logger.add(
        "./logs/bot.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG",
        rotation="1 day",
        retention="7 days"
    )

def signal_handler(signum, frame):
    """Обработчик сигналов"""
    logger.info(f"Received signal {signum}")
    sys.exit(0)

async def main():
    """Главная функция"""
    # Настраиваем логирование
    setup_logging()
    
    # Настраиваем обработчики сигналов
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Создаем и запускаем систему
    system = PostThiefBot()
    await system.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("👋 Goodbye!")
    except Exception as e:
        logger.error(f"💥 Fatal error: {e}")
        sys.exit(1)
