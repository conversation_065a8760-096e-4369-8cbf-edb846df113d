# 🚀 Быстрая настройка Post Thief Bot

## 1. Установка зависимостей

```bash
# Запустите install.bat или выполните команду:
pip install -r requirements.txt
```

## 2. Получение API ключей

### 🤖 Bot Token (от @BotFather)
1. Напишите @BotFather в Telegram
2. Отправьте `/newbot`
3. Следуйте инструкциям
4. Скопируйте полученный токен

### 🔑 API ID и API Hash (от my.telegram.org)
1. Перейдите на https://my.telegram.org
2. Войдите в свой аккаунт
3. Перейдите в "API development tools"
4. Создайте новое приложение
5. Скопируйте API ID и API Hash

### 🆔 Ваш Telegram ID
**Способ 1:** Автоматически
```bash
python get_telegram_id.py
```

**Способ 2:** Через бота
1. Напишите @userinfobot
2. Отправьте любое сообщение
3. Скопируйте ваш ID

### 🧠 ZeroEval API Key
Используйте ваш ключ от ZeroEval API

## 3. Настройка .env файла

Отредактируйте файл `.env`:

```env
# Telegram Bot API
BOT_TOKEN=ваш_токен_бота

# Telegram User Client
API_ID=ваш_api_id
API_HASH=ваш_api_hash

# AI API
ZEROEVAL_API_KEY=ваш_zeroeval_ключ

# Ваш Telegram ID
ADMIN_ID=ваш_telegram_id

# Шаблон рекламы (опционально)
DEFAULT_AD_TEMPLATE=🔥 Наша реклама здесь 🔥
```

## 4. Запуск

```bash
# Windows
start.bat

# Linux/Mac
python main.py
```

## 5. Первое использование

1. Отправьте `/start` вашему боту
2. Используйте `/add_channel` для добавления каналов
3. Бот начнет мониторинг автоматически
4. Новые посты будут приходить на модерацию

## ⚠️ Важно

- Ваш аккаунт должен быть подписан на все мониторимые каналы
- Бот работает только с публичными каналами или каналами, где вы участник
- Первый запуск может потребовать ввода кода подтверждения от Telegram

## 🆘 Проблемы?

1. Проверьте логи в папке `logs/`
2. Убедитесь, что все API ключи правильные
3. Проверьте подключение к интернету
4. Убедитесь, что вы подписаны на мониторимые каналы
