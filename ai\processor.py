import os
import re
from openai import OpenAI
from typing import Optional
from config.settings import settings
from loguru import logger

class AIProcessor:
    def __init__(self):
        self.client = OpenAI(
            base_url=settings.AI_BASE_URL,
            api_key=settings.ZEROEVAL_API_KEY
        )
    
    async def process_text(self, original_text: str, ad_template: str = None) -> Optional[str]:
        """
        Обработка текста: уникализация + замена рекламы
        """
        try:
            # Подготавливаем промпт для ИИ
            ad_template = ad_template or settings.DEFAULT_AD_TEMPLATE
            
            prompt = f"""
Ты - эксперт по рерайтингу контента для Telegram каналов. 

ЗАДАЧА:
1. Переписать текст, сделав его уникальным, но сохранив смысл и стиль
2. Удалить всю рекламу, ссылки на другие каналы, упоминания (@username)
3. Добавить в конец нашу рекламу: "{ad_template}"

ПРАВИЛА:
- Сохранить эмодзи и структуру текста
- Не изменять факты и цифры
- Писать естественно, как живой человек
- Убрать все ссылки и упоминания каналов
- Если текст короткий (меньше 50 символов), просто очистить от рекламы

ИСХОДНЫЙ ТЕКСТ:
{original_text}

ПЕРЕПИСАННЫЙ ТЕКСТ:"""

            response = self.client.chat.completions.create(
                model=settings.AI_MODEL,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            processed_text = response.choices[0].message.content.strip()
            
            # Дополнительная очистка
            processed_text = self._clean_text(processed_text)
            
            logger.info(f"Text processed successfully. Original length: {len(original_text)}, Processed length: {len(processed_text)}")
            return processed_text
            
        except Exception as e:
            logger.error(f"Error processing text with AI: {e}")
            # Fallback - простая очистка без ИИ
            return self._simple_clean(original_text, ad_template)
    
    def _clean_text(self, text: str) -> str:
        """Дополнительная очистка текста"""
        # Удаляем лишние пробелы и переносы
        text = re.sub(r'\n{3,}', '\n\n', text)
        text = re.sub(r' {2,}', ' ', text)
        
        # Удаляем оставшиеся упоминания и ссылки
        text = re.sub(r'@\w+', '', text)
        text = re.sub(r'https?://\S+', '', text)
        text = re.sub(r't\.me/\S+', '', text)
        
        return text.strip()
    
    def _simple_clean(self, text: str, ad_template: str) -> str:
        """Простая очистка без ИИ (fallback)"""
        # Удаляем упоминания и ссылки
        text = re.sub(r'@\w+', '', text)
        text = re.sub(r'https?://\S+', '', text)
        text = re.sub(r't\.me/\S+', '', text)
        
        # Удаляем рекламные блоки (простая эвристика)
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            # Пропускаем строки с рекламными словами
            if any(word in line.lower() for word in ['подписывайся', 'канал', 'реклама', 'промо']):
                continue
            if line:
                cleaned_lines.append(line)
        
        cleaned_text = '\n'.join(cleaned_lines)
        
        # Добавляем нашу рекламу
        if cleaned_text:
            cleaned_text += f"\n\n{ad_template}"
        
        return cleaned_text

# Глобальный экземпляр процессора
ai_processor = AIProcessor()
